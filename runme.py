import mcp
from mcp.client.streamable_http import streamablehttp_client
import json
import base64

# Doc:
config = {}
# Encode config in base64
config_b64 = base64.b64encode(json.dumps(config).encode())
smithery_api_key = "6aa2130b-8d12-4853-98ce-0b79580b209c"

# Create server URL
url = f"https://server.smithery.ai/@alperenkocyigit/call-for-papers-mcp/mcp?config={config_b64}&api_key={smithery_api_key}"

async def main():
    # Connect to the server using HTTP client
    async with streamablehttp_client(url) as (read_stream, write_stream, _):
        async with mcp.ClientSession(read_stream, write_stream) as session:
            # Initialize the connection
            await session.initialize()
            # List available tools
            tools_result = await session.list_tools()
            print(f"Available tools: {', '.join([t.name for t in tools_result.tools])}")

            # Call get_events with keywords and limit parameters
            result = await session.call_tool(
                "get_events",
                arguments={
                    "keywords": "machine learning",  # You can change this keyword
                    "limit": 10  # You can change this limit
                }
            )

            print("\nEvents result:")
            # Handle the result content properly
            for content in result.content:
                if hasattr(content, 'text'):
                    print(content.text)
                else:
                    print(content)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())